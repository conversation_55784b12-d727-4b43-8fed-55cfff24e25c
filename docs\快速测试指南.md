# 🚀 MSPM0G3507 循迹小车快速测试指南

## 📋 LED引脚确认

✅ **已确认您的LED配置：**
- **LED引脚**: GPIOB.9 (封装引脚61)
- **配置文件**: ti_msp_dl_config.h 已正确配置
- **初始化**: GPIO初始化代码已包含LED设置

## 🔧 三步测试方案

### 第一步：基础系统测试 ⭐ **推荐先做**

**文件**: `test_led_basic.c`

**操作步骤**:
1. 将 `test_led_basic.c` 的内容完全复制
2. 替换您的 `empty.c` 文件内容
3. 编译并烧录到MCU
4. 观察LED行为

**预期结果**:
- 启动时快速闪烁10次
- 长亮2秒表示初始化完成
- 进入循环测试模式 (快闪→慢闪→呼吸灯)
- 按KEY0可切换到按键测试模式

**故障排查**:
- ❌ 无任何反应 → 检查MCU供电和程序烧录
- ❌ LED不闪烁 → 检查LED硬件连接
- ✅ LED正常闪烁 → 系统基础功能正常，进行下一步

---

### 第二步：电机驱动测试

**文件**: `test_motor_simple.c`

**前提条件**: 第一步LED测试通过

**操作步骤**:
1. 确认电机驱动板和电机已连接
2. 将 `test_motor_simple.c` 内容替换 `empty.c`
3. 编译烧录

**预期结果**:
- LED闪烁5次 (启动)
- LED长亮2秒 (初始化)
- LED亮起时执行电机测试序列:
  - 左电机正转2秒 → 停1秒
  - 左电机反转2秒 → 停1秒  
  - 右电机正转2秒 → 停1秒
  - 右电机反转2秒 → 停1秒
  - 双电机同向2秒 → 停1秒
  - 双电机反向2秒 → 停3秒
- 循环重复

**故障排查**:
- ✅ LED正常但电机无反应 → 检查电机驱动板供电
- ✅ 电机转动但方向错误 → 检查AIN1/AIN2、BIN1/BIN2接线
- ✅ 电机正常工作 → 进行下一步

---

### 第三步：循迹传感器测试

**文件**: `test_line_sensors.c`

**前提条件**: 前两步测试通过

**操作步骤**:
1. 确认6路循迹传感器已连接
2. 将 `test_line_sensors.c` 内容替换 `empty.c`
3. 编译烧录

**预期结果**:
- LED闪烁3次 (启动)
- LED长亮2秒 (初始化)
- 运行时LED显示传感器状态:
  - 闪烁次数 = 检测到黑线的传感器数量
  - 快闪3次 = 偏左
  - 慢闪3次 = 偏右
  - 常亮1秒 = 居中

**测试方法**:
- 将传感器放在白纸上 → LED应该不闪烁或微弱闪烁
- 将黑色胶带放在传感器下 → LED闪烁次数增加
- 移动黑线位置 → 观察LED闪烁模式变化

---

## 🔌 硬件接线快速检查表

### 电机驱动 (必须连接)
- [ ] **AIN1** → PA16 (封装引脚9)
- [ ] **AIN2** → PA17 (封装引脚10)  
- [ ] **BIN1** → PA14 (封装引脚7)
- [ ] **BIN2** → PA13 (封装引脚6)
- [ ] **左电机PWM** → PB3 (封装引脚16)
- [ ] **右电机PWM** → PB2 (封装引脚15)
- [ ] **电机驱动板供电** → 5V-12V
- [ ] **GND共地**

### 循迹传感器 (可选)
- [ ] **HD0** → PA9  (封装引脚55)
- [ ] **HD1** → PA27 (封装引脚31)
- [ ] **HD2** → PA24 (封装引脚25)
- [ ] **HD3** → PB16 (封装引脚4)
- [ ] **HD4** → PA12 (封装引脚5)
- [ ] **HD5** → PB6  (封装引脚58)

### 按键和LED (已有)
- [ ] **LED** → PB9 (封装引脚61) ✅ 已确认
- [ ] **KEY0** → PA8 (封装引脚54)

---

## 🎯 成功标准

### 第一步成功标准:
- [x] LED能正常闪烁
- [x] 按键能切换模式
- [x] 系统稳定运行

### 第二步成功标准:
- [x] 左右电机能独立控制
- [x] 正反转方向正确
- [x] 无异常噪音或发热

### 第三步成功标准:
- [x] 传感器能区分黑白
- [x] LED指示与实际位置一致
- [x] 位置计算合理

---

## 🚨 常见问题解决

### Q1: LED完全不亮
**解决方案**:
1. 检查MCU供电 (3.3V)
2. 确认程序正确烧录
3. 检查LED硬件连接

### Q2: LED亮但电机不转
**解决方案**:
1. 检查电机驱动板供电 (5V-12V)
2. 确认PWM引脚连接 (PB2, PB3)
3. 检查方向控制引脚 (PA13, PA14, PA16, PA17)
4. 验证电机驱动板使能信号

### Q3: 电机转向错误
**解决方案**:
1. 交换AIN1和AIN2连接
2. 交换BIN1和BIN2连接
3. 或在代码中调整逻辑

### Q4: 循迹传感器无响应
**解决方案**:
1. 检查传感器供电 (3.3V或5V)
2. 确认6个GPIO连接正确
3. 测试传感器输出逻辑 (高/低电平)

---

## 💡 调试技巧

1. **逐步测试**: 不要跳过基础测试
2. **观察LED**: LED是最直观的调试工具
3. **检查供电**: 大部分问题都是供电不足
4. **确认接线**: 使用万用表验证连接
5. **分模块测试**: 先单独测试每个功能

---

## 📞 需要帮助？

如果按照此指南仍有问题，请提供：
1. 当前测试到第几步
2. LED的具体表现
3. 电机或传感器的具体现象
4. 硬件连接照片 (如可能)

**记住**: 先确保第一步LED测试完全正常，再进行后续测试！
