# MSPM0G3507 循迹小车接线说明和调试指南

## 📋 项目概述
基于MSPM0G3507微控制器的自平衡循迹小车，具备MPU6050姿态传感器、编码器反馈、循迹传感器等功能。

## 🔌 硬件接线说明

### 1. 电机驱动模块接线
**电机驱动控制引脚：**
- **AIN1** → PA16 (GPIOA.16, 封装引脚9)  - 左电机方向控制1
- **AIN2** → PA17 (GPIOA.17, 封装引脚10) - 左电机方向控制2  
- **BIN1** → PA14 (GPIOA.14, 封装引脚7)  - 右电机方向控制1
- **BIN2** → PA13 (GPIOA.13, 封装引脚6)  - 右电机方向控制2

**PWM速度控制引脚：**
- **左电机PWM** → PB3 (GPIOB.3, 封装引脚16) - PWM_0_C1
- **右电机PWM** → PB2 (GPIOB.2, 封装引脚15) - PWM_0_C0

**电源连接：**
- **VCC** → 电机驱动板电源正极 (通常5V-12V)
- **GND** → 电机驱动板电源负极和MCU共地

### 2. 编码器接线
**左编码器 (Encoder1)：**
- **E1A** → PB20 (GPIOB.20, 封装引脚19)
- **E1B** → PB24 (GPIOB.24, 封装引脚23)

**右编码器 (Encoder2)：**
- **E2A** → PA25 (GPIOA.25, 封装引脚26)  
- **E2B** → PA26 (GPIOA.26, 封装引脚30)

### 3. 循迹传感器接线 (6路红外传感器)
- **HD0** → PA9  (GPIOA.9,  封装引脚55) - 最左侧传感器
- **HD1** → PA27 (GPIOA.27, 封装引脚31) - 左侧传感器
- **HD2** → PA24 (GPIOA.24, 封装引脚25) - 左中传感器
- **HD3** → PB16 (GPIOB.16, 封装引脚4)  - 右中传感器
- **HD4** → PA12 (GPIOA.12, 封装引脚5)  - 右侧传感器
- **HD5** → PB6  (GPIOB.6,  封装引脚58) - 最右侧传感器

### 4. MPU6050姿态传感器接线
**I2C通信：**
- **SDA** → PA0 (GPIOA.0, 封装引脚33) - I2C数据线
- **SCL** → PA1 (GPIOA.1, 封装引脚34) - I2C时钟线
- **INT** → PA7 (GPIOA.7, 封装引脚49) - 中断引脚
- **VCC** → 3.3V
- **GND** → GND

### 5. 按键和LED
- **用户按键** → PA18 (GPIOA.18, 封装引脚11)
- **用户LED**  → PB9  (GPIOB.9,  封装引脚61)
- **KEY0**     → PA8  (GPIOA.8,  封装引脚54)

### 6. 电压检测
- **电池电压检测** → PA15 (GPIOA.15, 封装引脚8) - ADC输入

### 7. 串口通信 (可选)
**UART0 (调试串口)：**
- **TX** → PA10 (GPIOA.10, 封装引脚21)
- **RX** → PA11 (GPIOA.11, 封装引脚22)

**UART2 (K210通信)：**
- **TX** → PB17 (GPIOB.17, 封装引脚43)
- **RX** → PA22 (GPIOA.22, 封装引脚47)

## 🔧 分步调试指南

### 第一步：基础硬件检查
1. **电源检查**
   - 确认MCU供电正常 (3.3V)
   - 确认电机驱动板供电正常
   - 检查所有GND连接

2. **LED测试**
   - 烧录程序后观察PB9用户LED是否闪烁
   - 闪烁表示主程序正常运行

### 第二步：电机驱动测试
1. **连接电机驱动和电机**
   - 按照接线图连接电机驱动控制引脚
   - 连接PWM输出引脚
   - 连接电机到驱动板

2. **简单电机测试代码**
```c
// 在main函数while循环中添加测试代码
void motor_test() {
    // 测试左电机正转
    DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);
    DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 2000, GPIO_PWM_0_C1_IDX);
    delay_ms(2000);
    
    // 停止
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
    delay_ms(1000);
    
    // 测试右电机正转  
    DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);
    DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 2000, GPIO_PWM_0_C0_IDX);
    delay_ms(2000);
    
    // 停止
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C0_IDX);
    delay_ms(1000);
}
```

### 第三步：编码器测试
1. **检查编码器连接**
   - 确认编码器A、B相正确连接
   - 确认编码器供电正常

2. **编码器读取测试**
```c
// 在main函数while循环中添加
void encoder_test() {
    static int last_count_A = 0, last_count_B = 0;
    
    if(g_EncoderACount != last_count_A || g_EncoderBCount != last_count_B) {
        printf("EncoderA: %d, EncoderB: %d\n", g_EncoderACount, g_EncoderBCount);
        last_count_A = g_EncoderACount;
        last_count_B = g_EncoderBCount;
    }
}
```

### 第四步：循迹传感器测试
1. **传感器连接检查**
   - 确认6个循迹传感器正确连接到对应GPIO
   - 确认传感器供电正常

2. **传感器读取测试**
```c
// 在main函数while循环中添加
void line_sensor_test() {
    int sensors[6];
    sensors[0] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_0_PORT, GPIO_GRP_0_HDPIN_0_PIN);
    sensors[1] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_1_PORT, GPIO_GRP_0_HDPIN_1_PIN);
    sensors[2] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_2_PORT, GPIO_GRP_0_HDPIN_2_PIN);
    sensors[3] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_3_PORT, GPIO_GRP_0_HDPIN_3_PIN);
    sensors[4] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_4_PORT, GPIO_GRP_0_HDPIN_4_PIN);
    sensors[5] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_5_PORT, GPIO_GRP_0_HDPIN_5_PIN);
    
    printf("Sensors: %d %d %d %d %d %d\n", 
           sensors[0], sensors[1], sensors[2], sensors[3], sensors[4], sensors[5]);
}
```

### 第五步：MPU6050测试
1. **I2C通信检查**
   - 确认SDA、SCL连接正确
   - 确认MPU6050供电正常

2. **MPU6050初始化检查**
   - 观察程序是否能正常通过MPU6050初始化
   - 检查设备ID是否为0x68

### 第六步：系统集成测试
1. **按键控制测试**
   - 按下KEY0按键切换模式 (xuanti变量)
   - 按下用户按键启动/停止小车

2. **循迹功能测试**
   - 将小车放在黑线上
   - 观察循迹传感器读数
   - 测试循迹算法输出

## 🚨 常见问题排查

### 电机无反应
1. **检查PWM输出**
   - 用示波器检查PB2、PB3是否有PWM输出
   - 确认PWM频率和占空比设置正确

2. **检查方向控制**
   - 确认AIN1/AIN2、BIN1/BIN2电平正确
   - 检查电机驱动板使能信号

3. **电源问题**
   - 确认电机驱动板供电充足
   - 检查电流是否足够

### 编码器无计数
1. **检查中断配置**
   - 确认编码器中断已使能
   - 检查中断服务函数是否正确

2. **硬件连接**
   - 确认编码器A、B相连接正确
   - 检查编码器供电

### 循迹传感器异常
1. **传感器类型确认**
   - 确认是数字输出型传感器
   - 检查输出逻辑 (高电平/低电平表示检测到黑线)

2. **阈值调整**
   - 根据实际传感器调整检测阈值
   - 测试不同光照条件下的表现

## 📝 调试建议

1. **逐步测试**：先测试单个模块，再进行系统集成
2. **串口调试**：使用UART0输出调试信息
3. **LED指示**：利用用户LED显示系统状态
4. **参数调整**：根据实际硬件调整PID参数和传感器阈值
5. **安全第一**：测试时确保小车在安全环境中运行

## 🔄 完整调试流程

1. 硬件连接 → 2. 基础功能测试 → 3. 传感器校准 → 4. 控制算法调试 → 5. 系统优化

按照此指南逐步进行，应该能够成功让您的循迹小车正常工作！
