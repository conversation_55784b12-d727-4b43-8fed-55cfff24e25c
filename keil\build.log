*** Using Compiler 'V6.22', folder: 'D:\Keil\ARM\ARMCLANG\Bin'
Build target 'MSPM0G3507_Project'
Before Build - User command #1: cmd.exe /C "D:\TI\mspm0_sdk_2_00_01_00\2025CODE727\keil\../tools/keil/syscfg.bat 'D:\TI\mspm0_sdk_2_00_01_00\2025CODE727\keil\' empty.syscfg"

Couldn't find Sysconfig Tool "C:\ti\sysconfig_1.20.0\sysconfig_cli.bat"
"Update the file located at <sdk path>/tools/keil/syscfg.bat"

compiling empty.c...
linking...
.\Objects\empty_LP_MSPM0G3507_nortos_keil.axf: Error: L6200E: Symbol ADC1_IRQHandler multiply defined (by balance.o and empty.o).
Not enough information to list image symbols.
Not enough information to list load addresses in the image map.
Finished: 2 information, 0 warning and 1 error messages.
".\Objects\empty_LP_MSPM0G3507_nortos_keil.axf" - 1 Error(s), 0 Warning(s).
Target not created.
Build Time Elapsed:  00:00:03
