/*
 * 最基础的LED测试代码 - 验证系统基本功能
 * 已确认LED配置：GPIOB.9 (封装引脚61)
 * 直接替换empty.c文件内容，编译烧录即可
 */

#include "ti_msp_dl_config.h"

// 全局变量声明 (兼容原代码)
volatile short g_EncoderACount = 0;
volatile short g_EncoderBCount = 0;
uint8_t Flag_Stop = 0;

// 简单延时函数
void delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 8000; j++) {
            __NOP();
        }
    }
}

// 中断服务函数 (保持兼容性)
void GPIOA_IRQHandler(void) {
    // 空的中断处理函数，防止编译错误
}

void GPIOB_IRQHandler(void) {
    // 空的中断处理函数，防止编译错误  
}

void ADC1_IRQHandler(void) {
    // 空的ADC中断处理函数
}

void UART0_IRQHandler(void) {
    // 空的UART中断处理函数
}

void UART2_IRQHandler(void) {
    // 空的UART中断处理函数
}

void TIMA1_IRQHandler(void) {
    // 空的定时器中断处理函数
}

// LED测试模式
void led_test_pattern_1() {
    // 模式1: 快速闪烁 - 表示系统正常启动
    for(int i = 0; i < 10; i++) {
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(100);
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(100);
    }
}

void led_test_pattern_2() {
    // 模式2: 慢速闪烁 - 表示进入测试模式
    for(int i = 0; i < 5; i++) {
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(500);
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(500);
    }
}

void led_test_pattern_3() {
    // 模式3: 呼吸灯效果 - 表示系统稳定运行
    DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
    delay_ms(2000);  // 长亮2秒
    DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
    delay_ms(1000);  // 熄灭1秒
}

// 按键检测函数
int check_key_pressed() {
    // 检查KEY0按键 (PA8)
    if(DL_GPIO_readPins(GPIO_GRP_0_KEY0_PORT, GPIO_GRP_0_KEY0_PIN) == 0) {
        delay_ms(50);  // 防抖
        if(DL_GPIO_readPins(GPIO_GRP_0_KEY0_PORT, GPIO_GRP_0_KEY0_PIN) == 0) {
            return 1;  // 按键按下
        }
    }
    return 0;  // 按键未按下
}

// 主函数 - 最基础的系统测试
int main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    
    int test_mode = 0;  // 测试模式: 0=启动测试, 1=循环测试, 2=按键测试
    int cycle_count = 0;
    
    // 启动测试序列
    led_test_pattern_1();  // 快速闪烁表示启动
    delay_ms(1000);
    
    led_test_pattern_3();  // 长亮表示初始化完成
    
    // 主循环
    while(1) {
        switch(test_mode) {
            case 0:  // 启动测试模式
                led_test_pattern_2();  // 慢速闪烁
                test_mode = 1;  // 切换到循环测试
                break;
                
            case 1:  // 循环测试模式
                // 每个周期不同的LED模式
                if(cycle_count % 3 == 0) {
                    led_test_pattern_1();  // 快闪
                } else if(cycle_count % 3 == 1) {
                    led_test_pattern_2();  // 慢闪
                } else {
                    led_test_pattern_3();  // 呼吸
                }
                
                cycle_count++;
                
                // 检查是否按键切换模式
                if(check_key_pressed()) {
                    test_mode = 2;
                    cycle_count = 0;
                    // 按键确认 - 快闪3次
                    for(int i = 0; i < 3; i++) {
                        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
                        delay_ms(150);
                        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
                        delay_ms(150);
                    }
                    delay_ms(1000);  // 等待按键释放
                }
                break;
                
            case 2:  // 按键测试模式
                // 按键控制LED
                if(check_key_pressed()) {
                    DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
                    delay_ms(100);
                    DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
                    delay_ms(100);
                    cycle_count++;
                    
                    // 按键10次后返回循环模式
                    if(cycle_count >= 10) {
                        test_mode = 1;
                        cycle_count = 0;
                        // 模式切换确认 - 长亮2秒
                        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
                        delay_ms(2000);
                        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
                    }
                    
                    delay_ms(500);  // 防止重复触发
                } else {
                    // 无按键时微弱闪烁表示等待
                    DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
                    delay_ms(50);
                    DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
                    delay_ms(950);
                }
                break;
                
            default:
                test_mode = 0;
                break;
        }
        
        delay_ms(100);  // 主循环延时
    }
}

/*
 * 使用说明：
 * 
 * 1. 直接替换empty.c文件内容
 * 2. 编译并烧录到MCU
 * 3. 观察LED行为：
 * 
 * 启动序列：
 * - 快速闪烁10次 (启动)
 * - 长亮2秒 (初始化完成)
 * - 慢速闪烁5次 (进入测试)
 * 
 * 循环测试模式：
 * - 周期1: 快速闪烁
 * - 周期2: 慢速闪烁  
 * - 周期3: 呼吸灯效果
 * - 按KEY0切换到按键测试模式
 * 
 * 按键测试模式：
 * - 按KEY0时LED闪烁一次
 * - 无按键时微弱闪烁等待
 * - 按键10次后自动返回循环模式
 * 
 * 故障排查：
 * - 无任何LED反应: 检查MCU供电和程序烧录
 * - LED不按预期闪烁: 检查LED连接或更换LED
 * - 按键无反应: 检查KEY0 (PA8)连接
 * 
 * 成功标志：
 * - LED能按序列正常闪烁
 * - 按键能正常切换模式
 * - 系统稳定运行无重启
 */
