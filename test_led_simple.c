/*
 * 最简单的LED测试代码 - 验证系统基本功能
 * 已确认LED配置：GPIO_GRP_0_UserLED (GPIOB.9, 封装引脚61)
 * 
 * 编译方法：
 * 1. 将此文件重命名为 empty.c 替换原文件
 * 2. 或者在Keil项目中添加此文件并排除原empty.c
 * 3. 编译烧录即可
 * 
 * 预期现象：
 * - 上电后LED快速闪烁10次（表示系统启动）
 * - 然后LED每秒闪烁一次（表示系统正常运行）
 */

#include "ti_msp_dl_config.h"
#include "bsp_systick.h"

// 全局变量声明 (保持与原项目兼容)
extern uint8_t Flag_Stop;
extern int Turn1;
extern int Velocity1;
volatile short g_EncoderACount = 0;
volatile short g_EncoderBCount = 0;

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 启用必要的中断
    NVIC_EnableIRQ(GPIOA_INT_IRQN);
    NVIC_EnableIRQ(GPIOB_INT_IRQN);
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_2_INST_INT_IRQN);
    NVIC_EnableIRQ(TIMER_1_INST_INT_IRQN);
    NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    
    // 延时初始化
    delay_init();
    
    // 启动指示：快速闪烁10次
    for(int i = 0; i < 10; i++) {
        DL_GPIO_setPins(GPIO_GRP_0_UserLED_PORT, GPIO_GRP_0_UserLED_PIN);
        delay_ms(100);
        DL_GPIO_clearPins(GPIO_GRP_0_UserLED_PORT, GPIO_GRP_0_UserLED_PIN);
        delay_ms(100);
    }
    
    // 主循环：每秒闪烁一次表示系统正常运行
    while (1) {
        DL_GPIO_setPins(GPIO_GRP_0_UserLED_PORT, GPIO_GRP_0_UserLED_PIN);
        delay_ms(500);
        DL_GPIO_clearPins(GPIO_GRP_0_UserLED_PORT, GPIO_GRP_0_UserLED_PIN);
        delay_ms(500);
    }
}
