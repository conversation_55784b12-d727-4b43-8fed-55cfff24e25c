/*
 * 循迹传感器测试代码 - 可直接烧录运行
 * 已确认LED配置：GPIOB.9 (封装引脚61)
 * 替换empty.c文件内容，直接编译烧录即可
 */

#include "ti_msp_dl_config.h"
#include <stdio.h>

// 全局变量声明 (兼容原代码)
volatile short g_EncoderACount = 0;
volatile short g_EncoderBCount = 0;
uint8_t Flag_Stop = 0;

// 延时函数
void delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 8000; j++) {
            __NOP();
        }
    }
}

// 读取所有循迹传感器
void read_line_sensors(int *sensors) {
    sensors[0] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_0_PORT, GPIO_GRP_0_HDPIN_0_PIN) ? 1 : 0;  // 最左
    sensors[1] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_1_PORT, GPIO_GRP_0_HDPIN_1_PIN) ? 1 : 0;  // 左
    sensors[2] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_2_PORT, GPIO_GRP_0_HDPIN_2_PIN) ? 1 : 0;  // 左中
    sensors[3] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_3_PORT, GPIO_GRP_0_HDPIN_3_PIN) ? 1 : 0;  // 右中
    sensors[4] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_4_PORT, GPIO_GRP_0_HDPIN_4_PIN) ? 1 : 0;  // 右
    sensors[5] = DL_GPIO_readPins(GPIO_GRP_0_HDPIN_5_PORT, GPIO_GRP_0_HDPIN_5_PIN) ? 1 : 0;  // 最右
}

// 通过LED显示传感器状态 (二进制编码)
void display_sensors_on_led(int *sensors) {
    // 将6个传感器状态编码为LED闪烁次数
    int total = sensors[0] + sensors[1] + sensors[2] + sensors[3] + sensors[4] + sensors[5];
    
    // LED闪烁次数表示检测到黑线的传感器数量
    for(int i = 0; i < total; i++) {
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(200);
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(200);
    }
    
    // 长暂停表示一次读取结束
    delay_ms(1000);
}

// 计算循迹位置 (仿照原代码的xunji函数逻辑)
float calculate_line_position(int *sensors) {
    float score = 0;
    
    if (sensors[0] > 0) score += 15;   // 最左侧权重最大
    if (sensors[1] > 0) score += 10;   // 左侧
    if (sensors[2] > 0) score += 4;    // 左中
    if (sensors[3] > 0) score += -4;   // 右中
    if (sensors[4] > 0) score += -10;  // 右侧  
    if (sensors[5] > 0) score += -15;  // 最右侧权重最大
    
    return score;
}

// 通过LED显示位置信息
void display_position_on_led(float position) {
    if (position > 10) {
        // 偏左 - LED快闪
        for(int i = 0; i < 3; i++) {
            DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
            delay_ms(100);
            DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
            delay_ms(100);
        }
    } else if (position < -10) {
        // 偏右 - LED慢闪
        for(int i = 0; i < 3; i++) {
            DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
            delay_ms(300);
            DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
            delay_ms(300);
        }
    } else {
        // 居中 - LED常亮
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(1000);
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
    }
}

// 传感器校准测试
void sensor_calibration_test() {
    int sensors[6];
    int test_count = 0;
    
    while(test_count < 20) {  // 进行20次测试
        read_line_sensors(sensors);
        
        // 显示当前传感器状态
        printf("Test %d: Sensors [%d %d %d %d %d %d]\n", 
               test_count + 1,
               sensors[0], sensors[1], sensors[2], 
               sensors[3], sensors[4], sensors[5]);
        
        // 计算位置
        float position = calculate_line_position(sensors);
        printf("Position: %.2f\n", position);
        
        // LED显示
        display_sensors_on_led(sensors);
        
        test_count++;
        delay_ms(500);
    }
}

// 中断服务函数 (保持兼容性)
void GPIOA_IRQHandler(void) {
    // 空的中断处理函数，防止编译错误
}

void GPIOB_IRQHandler(void) {
    // 空的中断处理函数，防止编译错误
}

void ADC1_IRQHandler(void) {
    // 空的ADC中断处理函数
}

// 主函数 - 可直接替换empty.c内容
int main(void) {
    // 系统初始化
    SYSCFG_DL_init();

    // 启动LED指示系统启动
    for(int i = 0; i < 3; i++) {
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(500);
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(500);
    }

    // 长亮2秒表示初始化完成
    DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
    delay_ms(2000);
    DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
    delay_ms(1000);

    int sensors[6];
    int cycle_count = 0;

    while(1) {
        // 读取所有传感器
        read_line_sensors(sensors);

        // 计算位置
        float position = calculate_line_position(sensors);

        // 每10个周期显示一次传感器状态 (通过LED)
        if(cycle_count % 10 == 0) {
            display_sensors_on_led(sensors);
        }

        // 根据位置显示LED状态
        display_position_on_led(position);

        cycle_count++;
        delay_ms(500);  // 0.5秒检测一次
    }
}

/*
 * 使用说明：
 * 
 * 1. 硬件连接：
 *    - 确保6路循迹传感器已正确连接到对应GPIO
 *    - 传感器供电正常 (通常3.3V或5V)
 *    - 确认传感器输出类型 (数字输出)
 * 
 * 2. 测试步骤：
 *    a) 烧录此测试程序
 *    b) 打开串口监视器 (波特率9600)
 *    c) 观察LED和串口输出
 *    d) 将传感器放在不同位置测试
 * 
 * 3. 测试模式：
 *    - 连续监测模式：实时显示传感器状态和位置
 *    - 校准测试模式：按KEY0切换，进行20次连续测试
 * 
 * 4. 结果判断：
 *    - 传感器在白色表面：输出应为0
 *    - 传感器在黑线上：输出应为1
 *    - 位置计算：负值表示偏右，正值表示偏左，0表示居中
 * 
 * 5. LED指示含义：
 *    - 启动时闪烁3次
 *    - 运行时闪烁次数 = 检测到黑线的传感器数量
 *    - 快闪 = 偏左，慢闪 = 偏右，常亮 = 居中
 * 
 * 6. 故障排查：
 *    - 传感器无输出：检查供电和连线
 *    - 输出逻辑相反：可能需要调整传感器类型或代码逻辑
 *    - 灵敏度问题：调整传感器高度或阈值
 * 
 * 7. 成功标志：
 *    - 传感器能正确区分黑线和白色表面
 *    - 位置计算符合预期
 *    - LED指示与实际位置一致
 */
