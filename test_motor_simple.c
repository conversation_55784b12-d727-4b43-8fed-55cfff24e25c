/*
 * 简化电机测试代码 - 用于快速验证电机驱动连接
 * 将此代码替换到empty.c的main函数中进行测试
 */

#include "ti_msp_dl_config.h"
#include <stdlib.h>

// 延时函数
void delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 8000; j++) {
            __NOP();
        }
    }
}

// 电机控制函数
void set_motor_speed(int left_speed, int right_speed) {
    // 左电机方向控制
    if(left_speed > 0) {
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);      // AIN1 = 1
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);    // AIN2 = 0
    } else if(left_speed < 0) {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);    // AIN1 = 0  
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);      // AIN2 = 1
    } else {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);    // AIN1 = 0
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);    // AIN2 = 0
    }
    
    // 右电机方向控制
    if(right_speed > 0) {
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);      // BIN1 = 1
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);    // BIN2 = 0
    } else if(right_speed < 0) {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);    // BIN1 = 0
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);      // BIN2 = 1
    } else {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);    // BIN1 = 0
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);    // BIN2 = 0
    }
    
    // PWM速度控制 (0-8000范围)
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, abs(left_speed), GPIO_PWM_0_C1_IDX);   // 左电机PWM
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, abs(right_speed), GPIO_PWM_0_C0_IDX);  // 右电机PWM
}

// 电机测试序列
void motor_test_sequence() {
    // 测试1: 左电机正转
    set_motor_speed(2000, 0);  // 左电机中等速度正转，右电机停止
    delay_ms(2000);            // 运行2秒
    
    // 停止
    set_motor_speed(0, 0);
    delay_ms(1000);
    
    // 测试2: 左电机反转
    set_motor_speed(-2000, 0); // 左电机中等速度反转，右电机停止
    delay_ms(2000);
    
    // 停止
    set_motor_speed(0, 0);
    delay_ms(1000);
    
    // 测试3: 右电机正转
    set_motor_speed(0, 2000);  // 左电机停止，右电机中等速度正转
    delay_ms(2000);
    
    // 停止
    set_motor_speed(0, 0);
    delay_ms(1000);
    
    // 测试4: 右电机反转
    set_motor_speed(0, -2000); // 左电机停止，右电机中等速度反转
    delay_ms(2000);
    
    // 停止
    set_motor_speed(0, 0);
    delay_ms(1000);
    
    // 测试5: 双电机同向
    set_motor_speed(1500, 1500); // 双电机同向中等速度
    delay_ms(2000);
    
    // 停止
    set_motor_speed(0, 0);
    delay_ms(1000);
    
    // 测试6: 双电机反向 (原地转向)
    set_motor_speed(1500, -1500); // 左正转，右反转
    delay_ms(2000);
    
    // 停止
    set_motor_speed(0, 0);
    delay_ms(3000); // 长时间停止，准备下一轮测试
}

// 简化的主函数 - 替换原来的main函数内容
int main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    
    // 启动PWM定时器
    DL_TimerA_startCounter(PWM_0_INST);
    
    // LED闪烁指示系统启动
    for(int i = 0; i < 5; i++) {
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(200);
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        delay_ms(200);
    }
    
    // 主循环 - 持续进行电机测试
    while(1) {
        // LED指示测试开始
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
        
        // 执行电机测试序列
        motor_test_sequence();
        
        // LED指示测试结束
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        
        // 等待5秒后重复测试
        delay_ms(5000);
    }
}

/*
 * 使用说明：
 * 
 * 1. 将此代码内容替换到empty.c文件的main函数中
 * 2. 确保电机驱动板和电机已正确连接
 * 3. 烧录程序到MCU
 * 4. 观察电机运行情况：
 *    - LED闪烁5次表示系统启动
 *    - LED常亮期间进行电机测试
 *    - 测试序列：左正转→左反转→右正转→右反转→双电机同向→双电机反向
 *    - 每个动作持续2秒，间隔1秒
 *    - 完整测试周期约15秒，然后重复
 * 
 * 5. 故障排查：
 *    - 如果LED不闪烁：检查MCU供电和程序烧录
 *    - 如果LED闪烁但电机无反应：检查电机驱动板供电和连线
 *    - 如果电机转向错误：检查AIN1/AIN2、BIN1/BIN2连线
 *    - 如果电机速度异常：检查PWM连线和电机驱动板设置
 * 
 * 6. 成功标志：
 *    - 左右电机能够独立控制
 *    - 正反转方向正确
 *    - 速度控制有效
 *    - 无异常噪音或发热
 */
